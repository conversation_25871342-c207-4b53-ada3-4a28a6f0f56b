import React from "react";
import { NavigationContainer } from "@react-navigation/native";
import { AuthProvider, useAuth } from "./src/contexts/AuthContext";
import AppNavigator from "./src/navigation/AppNavigator";
import AuthNavigator from "./src/navigation/AuthNavigator";
import { ActivityIndicator, View } from "react-native";
import SelectCompanyScreen from "./src/screens/SelectCompany/SelectCompanyScreen";
import { VenueProvider, useVenue } from "./src/contexts/VenueContext";

function RootNavigator() {
  const { user, loading } = useAuth();
  const { company } = useVenue();

  if (loading) {
    return (
      <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
        <ActivityIndicator size="large" />
      </View>
    );
  }

  if (!user) return <AuthNavigator />;
  if (!company) return <SelectCompanyScreen />;
  return <AppNavigator />;
}

export default function App() {
  return (
    <AuthProvider>
        <VenueProvider>


            <NavigationContainer>
                <RootNavigator />
            </NavigationContainer>


        </VenueProvider>
    </AuthProvider>
  );
}
