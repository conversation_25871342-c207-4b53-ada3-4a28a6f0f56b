
import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { useNavigation } from '@react-navigation/native';

export const ShiftCard = () => {
  const navigation = useNavigation();
  const [isHovered, setIsHovered] = useState(false);

  const handlePress = () => {
    // navigation.navigate('ShiftDetails', { shift });
  };

  return (
    <TouchableOpacity
      onPress={handlePress}
      style={styles.container}
    >
      <View style={styles.imageContainer}>
        
      </View>
      <View style={styles.textContainer}>
        <Text style={styles.title}>serveur de nuit</Text>
        <Text style={styles.date}>test</Text>
        <Text style={styles.time}>test</Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    backgroundColor: '#fff',
    borderRadius: 10,
    margin: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  hovered: {
    backgroundColor: '#f0f0f0',
  },
  imageContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    overflow: 'hidden',
    marginRight: 10,
  },
  image: {
    width: '100%',
    height: '100%',
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  date: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  time: {
    fontSize: 14,
    color: '#666',
  },
});
