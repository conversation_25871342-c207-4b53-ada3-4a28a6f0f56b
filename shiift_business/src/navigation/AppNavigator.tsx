import React, { useState, useEffect } from "react";
import { Text } from "react-native";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import HomeStackNavigator from "./stacks/HomeStackNavigator";
import ProfilStackNavigator from "./stacks/ProfilStackNavigator";
import SelectCompanyScreen from "../screens/SelectCompany/SelectCompanyScreen";


import { useVenue } from "../contexts/VenueContext";

const Tab = createBottomTabNavigator();

export default function AppNavigator() {

    const { changeVenue, venue, company } = useVenue();

    useEffect(() => {
        console.log(changeVenue);
    }, [changeVenue]);

    

    if (changeVenue || !venue || !company) {
        console.log(!changeVenue , !venue , !company)
        return <SelectCompanyScreen />;
    }

    return (
        <Tab.Navigator screenOptions={{ headerShown: false }}>
            <Tab.Screen name="Home" component={HomeStackNavigator} />
            <Tab.Screen name="Profil" component={ProfilStackNavigator} />

        </Tab.Navigator>
    );
}
