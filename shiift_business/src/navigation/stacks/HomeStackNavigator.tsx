import React from "react";
import HomeScreen from "../../screens/Home/HomeScreen";
// import DetailsScreen from "../../screens/Home/DetailsScreen";
import { createNativeStackNavigator } from "@react-navigation/native-stack";
import ShiftsScreen from "../../screens/Home/ShiftsScreen";

const HomeStack = createNativeStackNavigator();


export default function HomeStackNavigator() {
    return (
      <HomeStack.Navigator>
        <HomeStack.Screen name="HomeMain" component={HomeScreen} options={{ title: "Accueil", headerShown: false }} />
        <HomeStack.Screen name="Shifts" component={ShiftsScreen} options={{ title: "Shifts" }} />
      </HomeStack.Navigator>
    );
  }