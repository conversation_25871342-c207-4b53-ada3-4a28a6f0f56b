import React from "react";
import ProfilScreen from "../../screens/Profil/ProfilScreen";
import { createNativeStackNavigator } from "@react-navigation/native-stack";

const HomeStack = createNativeStackNavigator();


export default function ProfilStackNavigator() {
    return (
      <HomeStack.Navigator>
        <HomeStack.Screen name="ProfilMain" component={ProfilScreen} options={{ title: "Profil" }} />
      </HomeStack.Navigator>
    );
  }