// src/api/user.ts
import api from "./client";
import { Shift } from "../types";

export async function getShifts(): Promise<Shift[]> {
    console.log("getShifts");
    const res = await api.get("/shifts");
    console.log("res : ", res);
    return res.data;
}

interface ShiftDetails {
  id: string;
  name: string;
  description: string;
}


export async function getShiftById(id: string): Promise<ShiftDetails> {
  const res = await api.get(`/shifts/${id}`);
  return res.data;
}
