// src/api/company.tsx
import { CompanyVenue } from "../types";
import api from "./client";

export type Company = {
  id: string;
  name: string;
};

export async function getCompanies(): Promise<Company[]> {
  const res = await api.get("/v1/companies");
//   console.log("res", res);
  return res.data.companies;
}


  
export async function getAllVenues(): Promise<CompanyVenue[]> {
    const res = await api.get("/v1/companies/venues/me");
    console.log("res", res.data);
    return res.data.companies;
}
  

