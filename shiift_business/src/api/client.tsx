// src/api/client.ts
import axios from "axios";
import * as SecureStore from "expo-secure-store";
import { BACKEND_URL } from "../constants/env";

const api = axios.create({
  baseURL: BACKEND_URL,
  timeout: 10000,
});

// Intercepteur pour ajouter le token dans chaque requête
api.interceptors.request.use(async (config) => {
  const token = await SecureStore.getItemAsync("access_token");
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
    config.headers["Content-Type"] = "application/json";
    config.headers["X-Client-App"] = "business";
  }
  return config;
});

// Gestion des erreurs globales
api.interceptors.response.use(
  (response) => response,
  (error) => {
    let message = "Erreur inconnue";
        console.error(error)

    if (error.response) {
      message = error.response.data?.message || `Erreur ${error.response.status}`;
    } else if (error.request) {
      message = "Impossible de joindre le serveur";
    } else {
      message = error.message;
    }
    console.error("API Error:", message);
    return Promise.reject(new Error(message));
  }
);

export default api;
