import React, { createContext, useContext, useState } from "react";
import { Company, Venue } from "../types";

type VenueContextType = {
    venue: Venue | null;
    setVenue: (venue: Venue | null) => void;
    company: Company | null;
    setCompany: (company: Company | null) => void;
    changeVenue: boolean;
    setChangeVenue: (changeVenue: boolean) => void;
};

const VenueContext = createContext<VenueContextType | undefined>(undefined);

export const VenueProvider = ({ children }: { children: React.ReactNode }) => {
    const [venue, setVenue] = useState<Venue | null>(null);
    const [company, setCompany] = useState<Company | null>(null);
    const [changeVenue, setChangeVenue] = useState(false);

    return (
        <VenueContext.Provider value={{ venue, setVenue, company, setCompany, changeVenue, setChangeVenue }}>
            {children}
        </VenueContext.Provider>
    );
};

export const useVenue = () => {
    const ctx = useContext(VenueContext);
    if (!ctx) throw new Error("useVenue doit être utilisé dans VenueProvider");
    return ctx;
};