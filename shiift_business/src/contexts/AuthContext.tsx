import React, { createContext, useContext, useEffect, useState } from "react";
import * as SecureStore from "expo-secure-store";
import { supabase } from "../api/supabase";
import { BACKEND_URL } from "../constants/env";


type AuthContextType = {
    user: any | null;
    loading: boolean;
    signIn: (email: string, password: string) => Promise<void>;
    signOut: () => Promise<void>;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
    const [user, setUser] = useState<any | null>(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const init = async () => {
            const token = await SecureStore.getItemAsync("access_token");
            if (token) {
                const { data } = await supabase.auth.getUser(token);
                if (data.user) {
                    // Vérifier avec ton backend
                    const ok = await verifyWithBackend(token);
                    if (ok) setUser(data.user);
                    else await SecureStore.deleteItemAsync("access_token"); // token invalide
                }
            }

            setLoading(false);
            
        };
        init();
    }, []);

    // Vérifie le token auprès de ton backend
    const verifyWithBackend = async (token: string): Promise<boolean> => {
        try {
            const res = await fetch(`${BACKEND_URL}/v1/employers/me`, {
                method: "GET",
                headers: {
                Authorization: `Bearer ${token}`,
                "Content-Type": "application/json",
                "X-Client-App": "business",
                },
            });
            const data = await res.json();
            return res.ok;
        } catch (err) {
            console.error("Error Please retry.", err);
            return false;
        }
    };

    const signIn = async (email: string, password: string) => {
        const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
        });

        if (error) throw error;

        const access_token = data.session?.access_token;
        if (!access_token) throw new Error("Token non reçu");

        // Vérification avec ton backend
        const valid = await verifyWithBackend(access_token);
        console.log("valid", valid);
        if (!valid) throw new Error("Vous n'avez pas de compte business sur ce compte.");

        // Stockage sécurisé
        await SecureStore.setItemAsync("access_token", access_token);

        setUser(data.user);
    };

    const signOut = async () => {
        await supabase.auth.signOut();
        await SecureStore.deleteItemAsync("access_token");
        setUser(null);

    };


    return (
        <AuthContext.Provider value={{ user, loading, signIn, signOut }}>
            {children}
        </AuthContext.Provider>
    );
};

export const useAuth = () => {
    const ctx = useContext(AuthContext);
    if (!ctx) throw new Error("useAuth doit être utilisé dans AuthProvider");
    return ctx;
};