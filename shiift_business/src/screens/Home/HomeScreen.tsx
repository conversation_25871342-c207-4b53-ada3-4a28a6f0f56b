import React from "react";
import { View, Text, TouchableOpacity, StyleSheet, SafeAreaView, Image } from "react-native";
import { useNavigation } from "@react-navigation/native";
import LogoutButton from "../../components/LogoutButton";
import { useVenue } from "../../contexts/VenueContext";
import { useAuth } from "../../contexts/AuthContext";

export default function HomeScreen() {
  const navigation = useNavigation();
  const { company, setCompany, setChangeVenue } = useVenue();
  const { user } = useAuth();

  const buttons = [
    { title: "Voir mes shifts", screen: "Shifts", stats:"2" },
    { title: "Mon équipe", screen: "Team", stats:"2" },
    { title: "Statistiques", screen: "Stats", stats:"2" },
  ];

  return (
    <SafeAreaView style={styles.container}>
      {/* Header avec logo */}
      <View style={styles.header}>
        <Image source={require("../../../assets/icon.png")} style={styles.logo} />
        <TouchableOpacity
            onPress={(event) => {
                event.stopPropagation(); // Empêche la propagation
                setChangeVenue(true);
            }}
        >
            <Text style={styles.companyName}>{company?.name || "Bienvenue"} 🎉</Text>
        </TouchableOpacity>
      </View>

      {/* Grille de boutons */}
      <View style={styles.buttonsContainer}>
        {buttons.map((btn) => (
          <TouchableOpacity
            key={btn.title}
            style={styles.button}
            onPress={() => navigation.navigate(btn.screen as never)}
          >
            <Text style={styles.buttonText}>{btn.title}</Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Bouton déconnexion */}
      <View style={styles.logoutContainer}>
        <LogoutButton />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FAF9F9",
    paddingHorizontal: 16,
    paddingTop: 40,
  },
  header: {
    alignItems: "center",
    marginBottom: 40,
  },
  logo: {
    width: 80,
    height: 80,
    resizeMode: "contain",
    marginBottom: 12,
  },
  companyName: {
    fontSize: 20,
    fontWeight: "700",
    color: "#1A191B",
  },
  buttonsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
    gap: 16,
  },
  button: {
    backgroundColor: "#24D341",
    width: "48%",
    aspectRatio: 1,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOpacity: 0.1,
    shadowOffset: { width: 0, height: 4 },
    shadowRadius: 8,
    elevation: 5,
  },
  buttonText: {
    color: "#fff",
    fontWeight: "700",
    fontSize: 16,
    textAlign: "center",
  },
  logoutContainer: {
    marginTop: "auto",
    marginBottom: 32,
    alignItems: "center",
  },
});
