import React, { useState, useEffect } from "react";
import {
    View,
    Text,
    TouchableOpacity,
    StyleSheet,
    SafeAreaView,
    Image,
    ScrollView,
    Modal,
    TextInput,
} from "react-native";
import { useNavigation } from "@react-navigation/native";
import { useAuth } from "../../contexts/AuthContext";
import { ShiftCard } from "../../components/shifts/ShiftCard";

import { getShifts } from "../../api/shift";
import { useVenue } from "../../contexts/VenueContext";

import { Shift } from "../../types";

const ShiftsScreen = () => {
    const navigation = useNavigation();
    const { user } = useAuth();
    const { company } = useVenue();

    const [shifts, setShifts] = useState<Shift[]>([]);
    const [modalVisible, setModalVisible] = useState(false);

    // champs du formulaire
    const [title, setTitle] = useState("");
    const [date, setDate] = useState("");
    const [hours, setHours] = useState("");

    useEffect(() => {
        getShifts().then((shifts) => {
            setShifts(shifts);
        });
    }, []);

    const handleCreateShift = () => {
        if (!title || !date || !hours) return;

        const newShift: Shift = {
            id: Math.random().toString(),
            title,
            date,
            hours,
        };

        setShifts((prev) => [...prev, newShift]);
        setModalVisible(false);

        // reset champs
        setTitle("");
        setDate("");
        setHours("");
    };

    return (
        <SafeAreaView style={styles.container}>
            <View style={styles.header}>
                <Text style={styles.headerText}>Shifts</Text>
                <TouchableOpacity
                    style={styles.addButton}
                    onPress={() => setModalVisible(true)}
                >
                    <Text style={styles.addButtonText}>+ Shift</Text>
                </TouchableOpacity>
            </View>

            {/* Stats Widgets */}
            <View style={styles.statsContainer}>
                <View style={styles.statWidget}>
                    <Text style={styles.statValue}>{shifts.length}</Text>
                    <Text style={styles.statLabel}>Shifts</Text>
                </View>
                <View style={styles.statWidget}>
                    <Text style={styles.statValue}>40h</Text>
                    <Text style={styles.statLabel}>Total Hours</Text>
                </View>
                <View style={styles.statWidget}>
                    <Text style={styles.statValue}>$800</Text>
                    <Text style={styles.statLabel}>Earnings</Text>
                </View>
            </View>

            {/* Scroll Area for Cards */}
            <View style={{ flex: 1 }}>
                <Text style={{ margin: 16, fontWeight: "bold" }}>Upcoming Shifts</Text>
                <View style={styles.scrollArea}>
                    <ScrollView>
                        {shifts.map((shift) => (
                            <ShiftCard key={shift.id} shift={shift} />
                        ))}
                    </ScrollView>
                </View>
            </View>

            {/* Modal de création */}
            <Modal
                animationType="slide"
                transparent={true}
                visible={modalVisible}
                onRequestClose={() => setModalVisible(false)}
            >
                <View style={styles.modalOverlay}>
                    <View style={styles.modalContainer}>
                        <Text style={styles.modalTitle}>Créer un shift</Text>

                        <TextInput
                            style={styles.input}
                            placeholder="Titre"
                            value={title}
                            onChangeText={setTitle}
                        />
                        <TextInput
                            style={styles.input}
                            placeholder="Date (YYYY-MM-DD)"
                            value={date}
                            onChangeText={setDate}
                        />
                        <TextInput
                            style={styles.input}
                            placeholder="Heures"
                            value={hours}
                            onChangeText={setHours}
                        />

                        <View style={styles.modalButtons}>
                            <TouchableOpacity
                                style={[styles.modalButton, { backgroundColor: "#ccc" }]}
                                onPress={() => setModalVisible(false)}
                            >
                                <Text>Annuler</Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                                style={[styles.modalButton, { backgroundColor: "#24D341" }]}
                                onPress={handleCreateShift}
                            >
                                <Text style={{ color: "#fff" }}>Créer</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
            </Modal>
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    scrollArea: {
        flex: 1,
        padding: 16,
    },
    container: {
        flex: 1,
        backgroundColor: "#fff",
    },
    header: {
        backgroundColor: "#fff",
        padding: 10,
        borderBottomWidth: 1,
        borderBottomColor: "#ccc",
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
    },
    headerText: {
        fontSize: 20,
        fontWeight: "bold",
    },
    addButton: {
        backgroundColor: "#24D341",
        paddingVertical: 6,
        paddingHorizontal: 12,
        borderRadius: 8,
    },
    addButtonText: {
        color: "#fff",
        fontWeight: "bold",
    },
    statsContainer: {
        flexDirection: "row",
        justifyContent: "space-around",
        padding: 16,
        borderBottomWidth: 1,
        borderBottomColor: "#ccc",
    },
    statWidget: {
        alignItems: "center",
    },
    statValue: {
        fontSize: 24,
        fontWeight: "bold",
    },
    statLabel: {
        fontSize: 16,
        color: "#555",
    },
    modalOverlay: {
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
        backgroundColor: "rgba(0,0,0,0.5)",
    },
    modalContainer: {
        width: "90%",
        backgroundColor: "#fff",
        borderRadius: 12,
        padding: 20,
        elevation: 5,
    },
    modalTitle: {
        fontSize: 18,
        fontWeight: "bold",
        marginBottom: 12,
    },
    input: {
        borderWidth: 1,
        borderColor: "#ccc",
        borderRadius: 8,
        padding: 10,
        marginBottom: 12,
    },
    modalButtons: {
        flexDirection: "row",
        justifyContent: "space-between",
    },
    modalButton: {
        flex: 1,
        padding: 12,
        borderRadius: 8,
        alignItems: "center",
        marginHorizontal: 5,
    },
});

export default ShiftsScreen;
