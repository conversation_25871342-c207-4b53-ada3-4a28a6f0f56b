import React, { useState, useEffect } from "react";
import { View, Text, TouchableOpacity, StyleSheet, SafeAreaView, FlatList } from "react-native";
import { useNavigation } from "@react-navigation/native";
import { useVenue } from "../../contexts/VenueContext";
import { getAllVenues } from "../../api/company";
import { Company, Venue, CompanyVenue } from "../../types/companies";


export default function SelectCompanyScreen() {
    const { venue, setVenue, company, setCompany, setChangeVenue } = useVenue();
    const [loading, setLoading] = useState(true);
    const [companies, setCompanies] = useState<CompanyVenue[]>([]);


    const navigation = useNavigation();

    useEffect(() => {
        getAllVenues().then((data: CompanyVenue[]) => {
            console.log(data);
            setCompanies(data);
            setLoading(false);
        });
    }, []);
    
    if (loading) {
        return (
        <SafeAreaView style={styles.container}>
            <View style={styles.center}>
            <Text style={styles.loadingText}>Loading...</Text>
            </View>
        </SafeAreaView>
        );
    }
    return (
        <SafeAreaView style={styles.container}>
            <View style={styles.center}>
                <FlatList
                    data={companies}
                    keyExtractor={(item) => item.id.toString()}
                    renderItem={({ item: choices_company }) => (
                        <View style={{ marginBottom: 20, paddingHorizontal: 20, paddingVertical: 20, backgroundColor: "#FFFFFF", borderRadius: 12, borderWidth: 1, borderColor: "#1A191B22" }}>
                            <Text style={styles.title}>{choices_company.name}</Text>
                            <FlatList
                                data={choices_company.venues}
                                keyExtractor={(choices_venues) => choices_venues.id.toString()}
                                renderItem={({ item: choices_venues }) => (
                                    <TouchableOpacity
                                        style={[
                                            styles.listItem,
                                            choices_venues.id === venue?.id && styles.listItemSelected
                                        ]}
                                        onPress={() => {
                                            setVenue(choices_venues);
                                            setCompany(choices_company);
                                            setChangeVenue(false);
                                            console.log(choices_venues);
                                        }}
                                    >
                                        <Text style={styles.listItemText}>{choices_venues.name}</Text>
                                    </TouchableOpacity>
                                )}
                            ></FlatList>
                        </View>
                    )}
                />
            </View>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#FDF7FC", padding: 16 },
  title: { fontSize: 22, fontWeight: "700", color: "#1A191B", marginBottom: 20, textAlign: "center" },
  button: {
    backgroundColor: "#24D341",
    paddingVertical: 16,
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  buttonText: { color: "#FFF", fontSize: 18, fontWeight: "600", textAlign: "center" },
  listItem: {
    backgroundColor: "#FFFFFF",
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "#1A191B22",
    marginBottom: 12,
  },
  listItemText: { color: "#1A191B", fontSize: 16, fontWeight: "600" },
  changeCompany: { marginTop: 20, alignSelf: "center" },
  changeCompanyText: { color: "#24D341", fontWeight: "700" },
  center: { flex: 1, justifyContent: "center", alignItems: "center" },
  loadingText: { color: "#1A191B99" },
  listItemSelected: { backgroundColor: "#24D341", borderColor: "#24D341" },
});
