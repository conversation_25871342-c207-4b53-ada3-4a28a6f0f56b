// src/screens/Profile/ProfilScreen.tsx
import React, { useEffect, useState } from "react";
import { View, Text, Button, ActivityIndicator } from "react-native";
import { useAuth } from "../../contexts/AuthContext";
import { getProfile } from "../../api/profil";

export default function ProfilScreen() {
  const { signOut } = useAuth();
  const [profile, setProfile] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadProfile = async () => {
      try {
        const data = await getProfile();
        setProfile(data);
      } catch (err: any) {
        setError(err.message || "Erreur lors du chargement du profil");
      } finally {
        setLoading(false);
      }
    };
    loadProfile();
  }, []);

  if (loading) return <ActivityIndicator size="large" />;
  if (error) return <Text style={{ color: "red" }}>{error}</Text>;

  return (
    <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
      <Text style={{ fontSize: 20, marginBottom: 10 }}>Profil</Text>
      {profile ? (
        <>
          <Text>Nom : {profile.name}</Text>
          <Text>Email : {profile.email}</Text>
        </>
      ) : (
        <Text>Aucune donnée de profil</Text>
      )}

      <Button title="Se déconnecter" onPress={signOut} />
    </View>
  );
}
